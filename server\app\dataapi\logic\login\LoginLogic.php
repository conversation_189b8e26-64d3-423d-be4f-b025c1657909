<?php

namespace app\dataapi\logic\login;

use app\common\logic\BaseLogic;
use think\facade\Cache;
use app\dataapi\service\UserTokenService;
use app\dataapi\service\UserLoginService;
use think\facade\{Db, Config};
use app\common\service\ConfigService;
use app\common\model\sms\SmsCode;
use app\common\model\auth\Administrators;
use app\common\model\auth\Admin;
use app\adminapi\service\AdminTokenService;
/**
 * 登录逻辑
 * Class LoginLogic
 * @package app\dataapi\logic\user
 */
class LoginLogic extends BaseLogic
{
    /**
     * @notes 发送验证码
     * @param $params
     * @return false|mixed
     */
    public static function sendCode($params)
    {
        try {
            $mobile = $params['mobile'];

            $ip = request()->ip();

            // 获取配置的每日限制次数
            $smsCodeIp = ConfigService::get('sms', 'sms_code_ip', 0); // IP每日限制
            $smsCodeNum = ConfigService::get('sms', 'sms_code_num', 0); // 手机号每日限制

            // 检查IP每日请求次数限制
            if ($smsCodeIp > 0) {
                $ipCount = self::getTodaySmsCount('ip', $ip);
                if ($ipCount >= $smsCodeIp) {
                    throw new \Exception('该IP今日发送次数已达上限');
                }
            }

            $mobileCount = self::getTodaySmsCount('mobile', $mobile);
            // 检查手机号每日请求次数限制
            if ($smsCodeNum > 0) {
                if ($mobileCount >= $smsCodeNum) {
                    throw new \Exception('该手机号今日发送次数已达上限');
                }
            }

            // 缓存键定义
            $cacheKey = 'sms_code:' . $mobile;
            $timerKey = $cacheKey . '_timer';

            // 60秒限制检查
            if (Cache::has($timerKey)) {
                throw new \Exception('60秒内只能发送一次');
            }

            // 生成6位验证码
            $code = mt_rand(100000, 999999);
            $expireSeconds = 300; // 5分钟过期

            $isSms = ConfigService::get('sms', 'is_sms');
            $sms_engine = ConfigService::get('sms', 'engine','local');
            $sms_config = ConfigService::get('sms', $sms_engine,['name'=>'local']);
            // 初始化发送状态和结果
            $status = 1; // 默认成功
            $result = '';
            $content = "【" . $sms_config['name'] . "】您的验证码是" . $code . "。如非本人操作，请忽略本短信";
            $sms_engine = 'local';

            //查找配置信息是否开启短信
            
            if($isSms == 1){
                //发送短信
                $smsDriver = new \app\common\service\sms\SmsDriver();
                if (!$smsDriver->initialize()) {
                    throw new \Exception('短信服务初始化失败：' . $smsDriver->getError());
                }
                // 构建短信数据
                $smsData = [
                    'template_id' => $sms_config['secret_id'] ? $sms_config['secret_id'] : '',
                    'params' => [
                        'code' => $code
                    ],
                    'content'=>$content,
                ];

                // 发送短信
                $smsResult = $smsDriver->send($mobile, $smsData);
                if ($smsResult) {
                    $status = 1; // 成功
                    $result = is_array($smsResult) ? json_encode($smsResult, JSON_UNESCAPED_UNICODE) : (string)$smsResult;
                } else {
                    $status = 2; // 失败
                    $result = $smsDriver->getError() ?: '短信发送失败';
                    throw new \Exception('短信发送失败：' . $result);
                }
            }

            // 存储到数据库
            SmsCode::create([
                'mobile'      => $mobile,
                'code'        => $code,
                'scene'       => $params['scene'],
                'ip'          => $ip,
                'operators'   => $sms_engine,
                'times'       => $mobileCount + 1, // 当前次数+1
                'status'      => $status,
                'result'      => $result,
                'content'     => $content,
                'create_time' => time(),
                'expire_time' => time() + $expireSeconds
            ]);

            // 设置60秒计时锁
            Cache::set($timerKey, 1, 60);

            if($isSms == 1){
                $code = '';
            }
            return ['code'=>$code];
        } catch (\Exception $e) {
            self::$error = $e->getMessage();
            return false;
        }
    }


    /**
     * @notes 手机号密码注册
     * @param $params
     * @return array|false
     */
    public static function registerByCode($params)
    {
        try {
            //用户是否存在
            $user = Administrators::where('account', $params['mobile'])->where(['delete_time'=>null])->findOrEmpty();
            if(!$user->isEmpty()){
                throw new \Exception('用户已存在');
            }
            // // 验证码校验
            $codeRecord = SmsCode::where(['mobile'=>$params['mobile'],'code'=>$params['code']])
            ->order('id', 'desc')
            ->find();   
            if(!$codeRecord){
                throw new \Exception('验证码错误');
            }
            if (time() > $codeRecord['expire_time']) {
                throw new \Exception('验证码已过期');
            }
            $params['ip'] = request()->ip();
            $passwordSalt = Config::get('project.unique_identification');
            $inputPassword = create_password($params['password'], $passwordSalt);
            $params['password'] = $inputPassword;

            // 定义热门国家数组
            $hotCountries = [
                ['name' => '中国', 'img' => '/static/images/countries/china.png'],
                ['name' => '美国', 'img' => '/static/images/countries/usa.png'],
                ['name' => '日本', 'img' => '/static/images/countries/japan.png'],
                ['name' => '韩国', 'img' => '/static/images/countries/korea.png'],
                ['name' => '英国', 'img' => '/static/images/countries/uk.png'],
                ['name' => '法国', 'img' => '/static/images/countries/france.png'],
                ['name' => '德国', 'img' => '/static/images/countries/germany.png'],
                ['name' => '意大利', 'img' => '/static/images/countries/italy.png'],
                ['name' => '澳大利亚', 'img' => '/static/images/countries/australia.png'],
                ['name' => '加拿大', 'img' => '/static/images/countries/canada.png'],
            ];

            // 随机选择一个国家
            $randomCountry = $hotCountries[array_rand($hotCountries)];

            $array = [
                'root'          => 0,
                'name'          => nickname(),
                'avatar'        => '',
                'account'       => $params['mobile'],
                'password'      => $params['password'],
                'country'       => $randomCountry['name'],
                'country_img'   => $randomCountry['img'],
                'create_time'   => time(),
                'update_time'   => time(),
            ];
            $admin = Administrators::create($array);

            //生成用户访问令牌
            $adminInfo = AdminTokenService::setToken($admin->id, 1, 0);
            return $adminInfo;

        } catch (\Exception $e) {
            self::setError($e->getMessage());
            return false;
        }
    }

    /**
     * @notes 手机号密码登录
     * @param $params
     * @return array|false
     */
    public static function loginByMobile($params)
    {
        try {
            $admin = Admin::where('account', $params['mobile'])->findOrEmpty();

            $passwordSalt = Config::get('project.unique_identification');
            $inputPassword = create_password($params['password'], $passwordSalt);
            
            if($admin->isEmpty()){
                throw new \Exception('账号不存在');
            }
            if($admin->password !== $inputPassword){
                throw new \Exception('密码错误');
            }
             //生成用户访问令牌
            $adminInfo = AdminTokenService::setToken($admin->id, 1, 0);
            return $adminInfo;
        } catch (\Exception $e) {
            self::$error = $e->getMessage();
            return false;
        }
    }


    /**
     * @notes 获取今日短信发送次数
     * @param string $type 类型：ip 或 mobile
     * @param string $value IP地址或手机号
     * @return int
     */
    private static function getTodaySmsCount($type, $value)
    {
        $todayStart = strtotime(date('Y-m-d 00:00:00'));
        $todayEnd = strtotime(date('Y-m-d 23:59:59'));

        $where = [
            ['create_time', '>=', $todayStart],
            ['create_time', '<=', $todayEnd]
        ];

        if ($type === 'ip') {
            $where[] = ['ip', '=', $value];
        } elseif ($type === 'mobile') {
            $where[] = ['mobile', '=', $value];
        }

        return SmsCode::where($where)->count();
    }

    /**
     * @notes 退出登录
     * @param $userInfo
     * @return bool
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2022/9/16 17:56
     */
    public static function logout($userInfo)
    {
        //token不存在，不注销
        if (!isset($userInfo['token'])) {
            return false;
        }

        //设置token过期
        return UserTokenService::expireToken($userInfo['token']);
    }
}
