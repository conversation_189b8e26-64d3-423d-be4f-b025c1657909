<?php

namespace app\dataapi\validate\login;


use app\common\validate\BaseValidate;
use app\common\model\user\User;


/**
 * 短信验证
 * Class SmsValidate
 * @package app\dataapi\validate\user
 */
class LoginValidate extends BaseValidate
{
    protected $rule = [
        'mobile'        => 'require|mobile',
        'code'          => 'require|number|length:6',
        'password'      => 'require|min:6',
    ];

    protected $message = [
        'mobile.require'    => '手机号不能为空',
        'code.require'      => '验证码不能为空',
        'code.number'       => '验证码必须为数字',
        'code.length'       => '验证码长度错误',
        'password.require'  => '密码不能为空',
        'password.min'      => '密码不能少于6位',
    ];

    /**
     * @notes 密码登录场景
     * @return LoginValidate
     */
    public function scenePassword()
    {
        return $this->only(['mobile','password','device_name','device_id']);

    }
    /**
     * @notes 注册场景
     * @return LoginValidate
     */
    public function sceneRegister()
    {
        return $this->only(['mobile','code','password']);
    }
}
