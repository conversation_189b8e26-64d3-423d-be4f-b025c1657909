<?php

namespace app\dataapi\validate\login;



use app\common\validate\BaseValidate;


/**
 * 短信验证
 * Class SmsValidate
 * @package app\dataapi\validate\user
 */
class SendSmsValidate extends BaseValidate
{

    protected $rule = [
        'mobile'            => 'require',
        'scene'             => 'require|in:1,2,3,4'
    ];

    protected $message = [
        'mobile.require'    => '请输入手机号',
        'scene.require'     => '请选择场景',
        'scene.in'          => '参数错误',
    ];
}
