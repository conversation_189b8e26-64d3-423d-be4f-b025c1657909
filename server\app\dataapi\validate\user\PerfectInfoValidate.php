<?php

namespace app\dataapi\validate\user;


use app\common\validate\BaseValidate;

/**
 * 完善用户信息验证
 * Class PerfectInfoValidate
 * @package app\dataapi\validate\user
 */
class PerfectInfoValidate extends BaseValidate
{
    protected $rule = [
        'nickname' => 'require|max:7|regex:/^[\x{4e00}-\x{9fa5}]+$/u',
        'sex' => 'require|in:1,2',
        'birthday' => 'require|dateFormat:Y-m-d',
    ];

    protected $message = [
        'nickname.require' => '昵称不能为空',
        'nickname.max' => '昵称不能超过7个字符',
        'nickname.regex' => '昵称只能包含中文',
        'sex.require' => '性别不能为空',
        'sex.in' => '性别参数错误',
        'birthday.require' => '生日不能为空',
        'birthday.dateFormat' => '生日格式不正确，应为YYYY-MM-DD格式',
    ];
}
