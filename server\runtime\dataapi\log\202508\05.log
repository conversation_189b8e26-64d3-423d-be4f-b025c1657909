[2025-08-05T15:03:53+08:00][sql] CONNECT:[ UseTime:0.096133s ] mysql:host=**************;port=3306;dbname=data_api_rongbin;charset=utf8mb4
[2025-08-05T15:03:53+08:00][sql] SHOW FULL COLUMNS FROM `la_user_session` [ RunTime:0.062889s ]
[2025-08-05T15:03:53+08:00][sql] SELECT * FROM `la_user_session` WHERE  (  `token` IS NULL  AND `expire_time` > 1754377433 ) LIMIT 1 [ RunTime:0.063014s ]
[2025-08-05T15:05:30+08:00][sql] CONNECT:[ UseTime:0.083694s ] mysql:host=**************;port=3306;dbname=data_api_rongbin;charset=utf8mb4
[2025-08-05T15:05:30+08:00][sql] SHOW FULL COLUMNS FROM `la_user_session` [ RunTime:0.055009s ]
[2025-08-05T15:05:30+08:00][sql] SELECT * FROM `la_user_session` WHERE  (  `token` IS NULL  AND `expire_time` > 1754377530 ) LIMIT 1 [ RunTime:0.054886s ]
[2025-08-05T15:05:41+08:00][sql] CONNECT:[ UseTime:0.076463s ] mysql:host=**************;port=3306;dbname=data_api_rongbin;charset=utf8mb4
[2025-08-05T15:05:41+08:00][sql] SHOW FULL COLUMNS FROM `la_user_session` [ RunTime:0.056196s ]
[2025-08-05T15:05:41+08:00][sql] SELECT * FROM `la_user_session` WHERE  (  `token` IS NULL  AND `expire_time` > 1754377541 ) LIMIT 1 [ RunTime:0.050965s ]
[2025-08-05T15:06:21+08:00][sql] CONNECT:[ UseTime:0.092687s ] mysql:host=**************;port=3306;dbname=data_api_rongbin;charset=utf8mb4
[2025-08-05T15:06:22+08:00][sql] SHOW FULL COLUMNS FROM `la_user_session` [ RunTime:0.082470s ]
[2025-08-05T15:06:22+08:00][sql] SELECT * FROM `la_user_session` WHERE  (  `token` IS NULL  AND `expire_time` > 1754377581 ) LIMIT 1 [ RunTime:0.051724s ]
[2025-08-05T15:06:56+08:00][sql] CONNECT:[ UseTime:0.080722s ] mysql:host=**************;port=3306;dbname=data_api_rongbin;charset=utf8mb4
[2025-08-05T15:06:56+08:00][sql] SHOW FULL COLUMNS FROM `la_user_session` [ RunTime:0.051608s ]
[2025-08-05T15:06:56+08:00][sql] SELECT * FROM `la_user_session` WHERE  (  `token` IS NULL  AND `expire_time` > 1754377616 ) LIMIT 1 [ RunTime:0.052604s ]
[2025-08-05T15:11:18+08:00][sql] CONNECT:[ UseTime:0.095381s ] mysql:host=**************;port=3306;dbname=data_api_rongbin;charset=utf8mb4
[2025-08-05T15:11:18+08:00][sql] SHOW FULL COLUMNS FROM `la_user_session` [ RunTime:0.063211s ]
[2025-08-05T15:11:18+08:00][sql] SELECT * FROM `la_user_session` WHERE  (  `token` IS NULL  AND `expire_time` > 1754377878 ) LIMIT 1 [ RunTime:0.063475s ]
[2025-08-05T15:11:18+08:00][sql] SHOW FULL COLUMNS FROM `la_config` [ RunTime:0.062778s ]
[2025-08-05T15:11:18+08:00][sql] SELECT `value` FROM `la_config` WHERE  `type` = 'sms'  AND `name` = 'sms_code_ip' LIMIT 1 [ RunTime:0.066840s ]
[2025-08-05T15:11:18+08:00][sql] SELECT `value` FROM `la_config` WHERE  `type` = 'sms'  AND `name` = 'sms_code_num' LIMIT 1 [ RunTime:0.063006s ]
[2025-08-05T15:11:18+08:00][sql] SHOW FULL COLUMNS FROM `la_sms_code` [ RunTime:0.062913s ]
[2025-08-05T15:11:18+08:00][sql] SELECT COUNT(*) AS think_count FROM `la_sms_code` WHERE  (  `create_time` >= 1754323200  AND `create_time` <= 1754409599  AND `mobile` = '17310434080' ) [ RunTime:0.062309s ]
[2025-08-05T15:11:18+08:00][sql] SELECT `value` FROM `la_config` WHERE  `type` = 'sms'  AND `name` = 'is_sms' LIMIT 1 [ RunTime:0.062280s ]
[2025-08-05T15:11:18+08:00][sql] SELECT `value` FROM `la_config` WHERE  `type` = 'sms'  AND `name` = 'engine' LIMIT 1 [ RunTime:0.062644s ]
[2025-08-05T15:11:18+08:00][error] [0]app\common\service\ConfigService::get(): Argument #2 ($name) must be of type string, null given, called in D:\phpstudy_pro\WWW\data-center-manage\server\app\dataapi\logic\login\LoginLogic.php on line 74[D:\phpstudy_pro\WWW\data-center-manage\server\app\common\service\ConfigService.php:65]
[2025-08-05T15:13:09+08:00][sql] CONNECT:[ UseTime:0.080062s ] mysql:host=**************;port=3306;dbname=data_api_rongbin;charset=utf8mb4
[2025-08-05T15:13:09+08:00][sql] SHOW FULL COLUMNS FROM `la_user_session` [ RunTime:0.052464s ]
[2025-08-05T15:13:09+08:00][sql] SELECT * FROM `la_user_session` WHERE  (  `token` IS NULL  AND `expire_time` > 1754377989 ) LIMIT 1 [ RunTime:0.052876s ]
[2025-08-05T15:13:09+08:00][sql] SHOW FULL COLUMNS FROM `la_config` [ RunTime:0.052154s ]
[2025-08-05T15:13:09+08:00][sql] SELECT `value` FROM `la_config` WHERE  `type` = 'sms'  AND `name` = 'sms_code_ip' LIMIT 1 [ RunTime:0.051427s ]
[2025-08-05T15:13:09+08:00][sql] SELECT `value` FROM `la_config` WHERE  `type` = 'sms'  AND `name` = 'sms_code_num' LIMIT 1 [ RunTime:0.051581s ]
[2025-08-05T15:13:09+08:00][sql] SHOW FULL COLUMNS FROM `la_sms_code` [ RunTime:0.051967s ]
[2025-08-05T15:13:09+08:00][sql] SELECT COUNT(*) AS think_count FROM `la_sms_code` WHERE  (  `create_time` >= 1754323200  AND `create_time` <= 1754409599  AND `mobile` = '17310434080' ) [ RunTime:0.051580s ]
[2025-08-05T15:13:10+08:00][sql] SELECT `value` FROM `la_config` WHERE  `type` = 'sms'  AND `name` = 'is_sms' LIMIT 1 [ RunTime:0.052601s ]
[2025-08-05T15:13:10+08:00][sql] SELECT `value` FROM `la_config` WHERE  `type` = 'sms'  AND `name` = 'engine' LIMIT 1 [ RunTime:0.053713s ]
[2025-08-05T15:13:10+08:00][sql] SELECT `value` FROM `la_config` WHERE  `type` = 'sms'  AND `name` = 'local' LIMIT 1 [ RunTime:0.051321s ]
[2025-08-05T15:13:44+08:00][sql] CONNECT:[ UseTime:0.071327s ] mysql:host=**************;port=3306;dbname=data_api_rongbin;charset=utf8mb4
[2025-08-05T15:13:44+08:00][sql] SHOW FULL COLUMNS FROM `la_user_session` [ RunTime:0.047013s ]
[2025-08-05T15:13:45+08:00][sql] SELECT * FROM `la_user_session` WHERE  (  `token` IS NULL  AND `expire_time` > 1754378024 ) LIMIT 1 [ RunTime:0.047610s ]
[2025-08-05T15:13:45+08:00][sql] SHOW FULL COLUMNS FROM `la_config` [ RunTime:0.046649s ]
[2025-08-05T15:13:45+08:00][sql] SELECT `value` FROM `la_config` WHERE  `type` = 'sms'  AND `name` = 'sms_code_ip' LIMIT 1 [ RunTime:0.045908s ]
[2025-08-05T15:13:45+08:00][sql] SELECT `value` FROM `la_config` WHERE  `type` = 'sms'  AND `name` = 'sms_code_num' LIMIT 1 [ RunTime:0.046199s ]
[2025-08-05T15:13:45+08:00][sql] SHOW FULL COLUMNS FROM `la_sms_code` [ RunTime:0.047160s ]
[2025-08-05T15:13:45+08:00][sql] SELECT COUNT(*) AS think_count FROM `la_sms_code` WHERE  (  `create_time` >= 1754323200  AND `create_time` <= 1754409599  AND `mobile` = '17310434080' ) [ RunTime:0.046051s ]
[2025-08-05T15:13:45+08:00][sql] SELECT `value` FROM `la_config` WHERE  `type` = 'sms'  AND `name` = 'is_sms' LIMIT 1 [ RunTime:0.046794s ]
[2025-08-05T15:13:45+08:00][sql] SELECT `value` FROM `la_config` WHERE  `type` = 'sms'  AND `name` = 'engine' LIMIT 1 [ RunTime:0.046732s ]
[2025-08-05T15:13:45+08:00][sql] SELECT `value` FROM `la_config` WHERE  `type` = 'sms'  AND `name` = 'local' LIMIT 1 [ RunTime:0.047273s ]
[2025-08-05T15:13:45+08:00][sql] INSERT INTO `la_sms_code` SET `mobile` = '17310434080' , `code` = '711210' , `scene` = 1 , `ip` = '127.0.0.1' , `operators` = 'local' , `times` = 1 , `status` = 1 , `result` = '' , `content` = '【local】您的验证码是711210。如非本人操作，请忽略本短信' , `create_time` = 1754378025 , `expire_time` = 1754378325 [ RunTime:0.076702s ]
[2025-08-05T15:14:00+08:00][sql] CONNECT:[ UseTime:0.071269s ] mysql:host=**************;port=3306;dbname=data_api_rongbin;charset=utf8mb4
[2025-08-05T15:14:00+08:00][sql] SHOW FULL COLUMNS FROM `la_user_session` [ RunTime:0.047275s ]
[2025-08-05T15:14:00+08:00][sql] SELECT * FROM `la_user_session` WHERE  (  `token` IS NULL  AND `expire_time` > 1754378040 ) LIMIT 1 [ RunTime:0.048023s ]
[2025-08-05T15:14:01+08:00][sql] SHOW FULL COLUMNS FROM `la_config` [ RunTime:0.049002s ]
[2025-08-05T15:14:01+08:00][sql] SELECT `value` FROM `la_config` WHERE  `type` = 'sms'  AND `name` = 'sms_code_ip' LIMIT 1 [ RunTime:0.048183s ]
[2025-08-05T15:14:01+08:00][sql] SELECT `value` FROM `la_config` WHERE  `type` = 'sms'  AND `name` = 'sms_code_num' LIMIT 1 [ RunTime:0.047840s ]
[2025-08-05T15:14:01+08:00][sql] SHOW FULL COLUMNS FROM `la_sms_code` [ RunTime:0.049187s ]
[2025-08-05T15:14:01+08:00][sql] SELECT COUNT(*) AS think_count FROM `la_sms_code` WHERE  (  `create_time` >= 1754323200  AND `create_time` <= 1754409599  AND `mobile` = '17310434080' ) [ RunTime:0.046401s ]
[2025-08-05T15:16:47+08:00][sql] CONNECT:[ UseTime:0.084129s ] mysql:host=**************;port=3306;dbname=data_api_rongbin;charset=utf8mb4
[2025-08-05T15:16:47+08:00][sql] SHOW FULL COLUMNS FROM `la_user_session` [ RunTime:0.055881s ]
[2025-08-05T15:16:47+08:00][sql] SELECT * FROM `la_user_session` WHERE  (  `token` IS NULL  AND `expire_time` > 1754378207 ) LIMIT 1 [ RunTime:0.058381s ]
[2025-08-05T15:16:47+08:00][sql] SHOW FULL COLUMNS FROM `la_config` [ RunTime:0.062376s ]
[2025-08-05T15:16:47+08:00][sql] SELECT `value` FROM `la_config` WHERE  `type` = 'sms'  AND `name` = 'sms_code_ip' LIMIT 1 [ RunTime:0.070252s ]
[2025-08-05T15:16:47+08:00][sql] SELECT `value` FROM `la_config` WHERE  `type` = 'sms'  AND `name` = 'sms_code_num' LIMIT 1 [ RunTime:0.055193s ]
[2025-08-05T15:16:48+08:00][sql] SHOW FULL COLUMNS FROM `la_sms_code` [ RunTime:0.054560s ]
[2025-08-05T15:16:48+08:00][sql] SELECT COUNT(*) AS think_count FROM `la_sms_code` WHERE  (  `create_time` >= 1754323200  AND `create_time` <= 1754409599  AND `mobile` = '17310434080' ) [ RunTime:0.055586s ]
[2025-08-05T15:16:48+08:00][sql] SELECT `value` FROM `la_config` WHERE  `type` = 'sms'  AND `name` = 'is_sms' LIMIT 1 [ RunTime:0.054369s ]
[2025-08-05T15:16:48+08:00][sql] SELECT `value` FROM `la_config` WHERE  `type` = 'sms'  AND `name` = 'engine' LIMIT 1 [ RunTime:0.056209s ]
[2025-08-05T15:16:48+08:00][sql] SELECT `value` FROM `la_config` WHERE  `type` = 'sms'  AND `name` = 'local' LIMIT 1 [ RunTime:0.055377s ]
[2025-08-05T15:16:48+08:00][sql] INSERT INTO `la_sms_code` SET `mobile` = '17310434080' , `code` = '484476' , `scene` = 1 , `ip` = '127.0.0.1' , `operators` = 'local' , `times` = 2 , `status` = 1 , `result` = '' , `content` = '【local】您的验证码是484476。如非本人操作，请忽略本短信' , `create_time` = 1754378208 , `expire_time` = 1754378508 [ RunTime:0.056179s ]
[2025-08-05T15:19:07+08:00][sql] CONNECT:[ UseTime:0.093900s ] mysql:host=**************;port=3306;dbname=data_api_rongbin;charset=utf8mb4
[2025-08-05T15:19:07+08:00][sql] SHOW FULL COLUMNS FROM `la_user_session` [ RunTime:0.092156s ]
[2025-08-05T15:19:07+08:00][sql] SELECT * FROM `la_user_session` WHERE  (  `token` IS NULL  AND `expire_time` > 1754378347 ) LIMIT 1 [ RunTime:0.075341s ]
[2025-08-05T15:19:12+08:00][sql] CONNECT:[ UseTime:0.078888s ] mysql:host=**************;port=3306;dbname=data_api_rongbin;charset=utf8mb4
[2025-08-05T15:19:12+08:00][sql] SHOW FULL COLUMNS FROM `la_user_session` [ RunTime:0.052855s ]
[2025-08-05T15:19:12+08:00][sql] SELECT * FROM `la_user_session` WHERE  (  `token` IS NULL  AND `expire_time` > 1754378352 ) LIMIT 1 [ RunTime:0.054644s ]
[2025-08-05T15:35:51+08:00][sql] CONNECT:[ UseTime:0.085004s ] mysql:host=**************;port=3306;dbname=data_api_rongbin;charset=utf8mb4
[2025-08-05T15:35:51+08:00][sql] SHOW FULL COLUMNS FROM `la_user_session` [ RunTime:0.056892s ]
[2025-08-05T15:35:51+08:00][sql] SELECT * FROM `la_user_session` WHERE  (  `token` IS NULL  AND `expire_time` > 1754379351 ) LIMIT 1 [ RunTime:0.058269s ]
[2025-08-05T15:35:51+08:00][sql] SHOW FULL COLUMNS FROM `la_config` [ RunTime:0.056676s ]
[2025-08-05T15:35:51+08:00][sql] SELECT `value` FROM `la_config` WHERE  `type` = 'sms'  AND `name` = 'sms_code_ip' LIMIT 1 [ RunTime:0.055396s ]
[2025-08-05T15:35:51+08:00][sql] SELECT `value` FROM `la_config` WHERE  `type` = 'sms'  AND `name` = 'sms_code_num' LIMIT 1 [ RunTime:0.055870s ]
[2025-08-05T15:35:51+08:00][sql] SHOW FULL COLUMNS FROM `la_sms_code` [ RunTime:0.055836s ]
[2025-08-05T15:35:51+08:00][sql] SELECT COUNT(*) AS think_count FROM `la_sms_code` WHERE  (  `create_time` >= 1754323200  AND `create_time` <= 1754409599  AND `mobile` = '17310434080' ) [ RunTime:0.053807s ]
[2025-08-05T15:35:51+08:00][sql] SELECT `value` FROM `la_config` WHERE  `type` = 'sms'  AND `name` = 'is_sms' LIMIT 1 [ RunTime:0.053370s ]
[2025-08-05T15:35:51+08:00][sql] SELECT `value` FROM `la_config` WHERE  `type` = 'sms'  AND `name` = 'engine' LIMIT 1 [ RunTime:0.053932s ]
[2025-08-05T15:35:51+08:00][sql] SELECT `value` FROM `la_config` WHERE  `type` = 'sms'  AND `name` = 'local' LIMIT 1 [ RunTime:0.053721s ]
[2025-08-05T15:35:51+08:00][sql] INSERT INTO `la_sms_code` SET `mobile` = '17310434080' , `code` = '183937' , `scene` = 1 , `ip` = '127.0.0.1' , `operators` = 'local' , `times` = 3 , `status` = 1 , `result` = '' , `content` = '【local】您的验证码是183937。如非本人操作，请忽略本短信' , `create_time` = 1754379351 , `expire_time` = 1754379651 [ RunTime:0.056136s ]
