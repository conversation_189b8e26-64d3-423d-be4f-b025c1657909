<?php

namespace app\dataapi\controller\login;

use app\dataapi\logic\login\LoginLogic;
use app\dataapi\validate\login\SendSmsValidate;
use app\dataapi\validate\login\LoginValidate;
use app\dataapi\controller\BaseApiController;
/**
 * 登录
 * Class LoginController
 * @package app\api\controller
 */
class LoginController extends BaseApiController
{

    public array $notNeedLogin = ['send_code','login','register'];


    /**
     * @notes 发送短信验证码
     * @return \think\response\Json
     */
    public function send_code()
    {
        $params = (new SendSmsValidate())->post()->goCheck();
        $result = LoginLogic::sendCode($params);
        if ($result) {
            return $this->success('发送成功',$result,1,1);
        }
        return $this->fail(LoginLogic::getError());
    }

    /**
     * @notes 账号密码注册
     * @return \think\response\Json
     */
    public function register()
    {
        $params = (new LoginValidate())->post()->goCheck('register');
        $result = LoginLogic::registerByCode($params);
        if (false === $result) {
            return $this->fail(LoginLogic::getError());
        }
        return $this->success('注册成功',$result,1,1);
    }

    /**
     * @notes 手机号密码登录
     * @return \think\response\Json
     */
    public function login()
    {
        $params = (new LoginValidate())->post()->goCheck('password');
        $result = LoginLogic::loginByMobile($params);
        if (false === $result) {
            return $this->fail(LoginLogic::getError());
        }
        return $this->success('登录成功',$result,1,1);
    }

    /**
     * @notes 退出登录
     * @return \think\response\Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function logout()
    {
        $result =LoginLogic::logout($this->userInfo);
        if (false === $result) {
            return $this->fail(LoginLogic::getError());
        }
        return $this->success('退出成功',[],1,1);
    }
}