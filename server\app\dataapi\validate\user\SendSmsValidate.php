<?php

namespace app\dataapi\validate\user;



use app\common\validate\BaseValidate;


/**
 * 短信验证
 * Class SmsValidate
 * @package app\dataapi\validate\user
 */
class SendSmsValidate extends BaseValidate
{

    protected $rule = [
        'encrypted_data' => 'require',
        'scene'          => 'require|in:1,2,3,4'
    ];

    protected $message = [
        'encrypted_data.require' => '缺少参数',
        'scene.require'          => '缺少参数',
        'scene.in'               => '参数错误',
    ];
}
