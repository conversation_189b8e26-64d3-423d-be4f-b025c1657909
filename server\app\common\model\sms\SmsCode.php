<?php

namespace app\common\model\sms;

use app\common\model\BaseModel;

/**
 * 短信验证码模型
 * Class SmsCode
 * @package app\common\model\sms
 */
class SmsCode extends BaseModel
{
    protected $name = 'sms_code';
    
    protected $pk = 'id';

    /**
     * @notes 设置字段信息
     * @return string[]
     * <AUTHOR> @date 2024/01/01 00:00
     */
    public function setMobileAttr($value)
    {
        return $value;
    }

    public function setCodeAttr($value)
    {
        return $value;
    }

    public function setSceneAttr($value)
    {
        return $value;
    }

    public function setIpAttr($value)
    {
        return $value;
    }

    public function setOperatorsAttr($value)
    {
        return $value;
    }

    public function setTimesAttr($value)
    {
        return $value;
    }

    public function setStatusAttr($value)
    {
        return $value;
    }

    public function setResultAttr($value)
    {
        return $value;
    }

    public function setContentAttr($value)
    {
        return $value;
    }

    public function setCreateTimeAttr($value)
    {
        return $value;
    }

    public function setExpireTimeAttr($value)
    {
        return $value;
    }
}
