<?php

namespace app\dataapi\validate\user;


use app\common\validate\BaseValidate;
use app\common\model\user\User;


/**
 * 短信验证
 * Class SmsValidate
 * @package app\dataapi\validate\user
 */
class LoginValidate extends BaseValidate
{
    protected $rule = [
        // 'mobile' => 'require|mobile|unique:'.User::class.',mobile',
        'sign'       => 'require',
        'mobile' => 'require|mobile',
        'code'   => 'require|number|length:6',
        'terminal'   => 'require',
        'password'   => 'require|min:6',
        
        'device_name' => 'require',
        'device_id' => 'require'
    ];

    protected $message = [
        'sign.require' => '签名不能为空',
        'mobile.require' => '手机号不能为空',
        'mobile.mobile' => '手机号格式错误',
        // 'mobile.unique' => '手机号已存在',
        'code.require'  => '验证码不能为空',
        'code.number'   => '验证码必须为数字',
        'code.length'   => '验证码长度错误',
        'terminal.require' => '客户端类型不能为空',
        'password.require' => '密码不能为空',
        'password.min' => '密码不能少于6位',
        
        'device_name.require' => '设备名称不能为空',
        'device_id.require' => '设备ID不能为空'
    ];

    /**
     * @notes 协议场景
     * @return LoginValidate
     */
    public function sceneConfig()
    {
        return $this->only(['sign']);
    }
    /**
     * @notes 验证码登录场景
     * @return LoginValidate
     */
    public function sceneCode()
    {
        return $this->only(['sign','terminal','code','device_name','device_id']);

    }

    /**
     * @notes 密码登录场景
     * @return LoginValidate
     */
    public function scenePassword()
    {
        return $this->only(['sign','terminal','password','device_name','device_id']);

    }

    /**
     * @notes 注册场景
     * @return LoginValidate
     */
    public function sceneRegister()
    {
        return $this->only(['sign','terminal','code','password']);
    }

    /**
     * @notes 修改密码场景
     * @return LoginValidate
     */
    public function sceneChangePassword()
    {
        return $this->only(['sign','password']);
    }

    /**
     * @notes 验证验证码场景
     * @return LoginValidate
     */
    public function sceneVerifyCode()
    {
        return $this->only(['sign','code']);
    }

    /**
     * @notes 注销账号场景
     * @return LoginValidate
     */
    public function scenelogoutAccount()
    {
        return $this->only(['sign','code']);
    }
}
